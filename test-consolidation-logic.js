// Test script to verify the improved consolidateChangesByType logic
// This simulates the scenario: addNode A → addNode B → update node B → update node A

const testChanges = [
  // Event 1: Add Node A (timestamp: 1000)
  {
    type: 'granular_nodes',
    roomId: 'test-room',
    userId: 'user1',
    data: {
      changes: [
        {
          type: 'add',
          item: {
            id: 'nodeA',
            type: 'custom',
            position: { x: 100, y: 100 },
            data: { label: 'Node A' }
          }
        }
      ]
    },
    timestamp: 1000
  },
  
  // Event 2: Add Node B (timestamp: 1001)
  {
    type: 'granular_nodes',
    roomId: 'test-room',
    userId: 'user1',
    data: {
      changes: [
        {
          type: 'add',
          item: {
            id: 'nodeB',
            type: 'custom',
            position: { x: 200, y: 200 },
            data: { label: 'Node B' }
          }
        }
      ]
    },
    timestamp: 1001
  },
  
  // Event 3: Update Node B (timestamp: 1002)
  {
    type: 'granular_nodes',
    roomId: 'test-room',
    userId: 'user1',
    data: {
      changes: [
        {
          type: 'replace',
          id: 'nodeB',
          item: {
            id: 'nodeB',
            type: 'custom',
            position: { x: 200, y: 200 },
            data: { label: 'Node B Updated' }
          }
        }
      ]
    },
    timestamp: 1002
  },
  
  // Event 4: Update Node A (timestamp: 1003)
  {
    type: 'granular_nodes',
    roomId: 'test-room',
    userId: 'user1',
    data: {
      changes: [
        {
          type: 'replace',
          id: 'nodeA',
          item: {
            id: 'nodeA',
            type: 'custom',
            position: { x: 100, y: 100 },
            data: { label: 'Node A Updated' }
          }
        }
      ]
    },
    timestamp: 1003
  }
]

console.log('=== TEST SCENARIO ===')
console.log('Input changes:')
testChanges.forEach((change, index) => {
  const changeData = change.data.changes[0]
  console.log(`${index + 1}. ${change.type} - ${changeData.type} ${changeData.item?.id || changeData.id} (timestamp: ${change.timestamp})`)
})

console.log('\n=== EXPECTED RESULT WITH NEW LOGIC ===')
console.log('Consolidated granular_nodes should contain ALL 4 changes:')
console.log('1. Add nodeA')
console.log('2. Add nodeB') 
console.log('3. Replace nodeB')
console.log('4. Replace nodeA')
console.log('\nWhen applied with React Flow\'s applyNodeChanges:')
console.log('- nodeA will be added, then updated')
console.log('- nodeB will be added, then updated')
console.log('- Final result: Both nodes exist with updated labels')

console.log('\n=== COMPARISON WITH OLD LOGIC ===')
console.log('Old logic would only keep the LAST change:')
console.log('- Only "Replace nodeA" would survive')
console.log('- This would fail because nodeA doesn\'t exist')
console.log('- Result: Operation fails, no changes applied')

// Test with bulk operation interference
console.log('\n=== TEST WITH BULK OPERATION ===')
const testWithBulk = [
  ...testChanges.slice(0, 2), // Add nodeA, Add nodeB
  
  // Bulk operation at timestamp 1001.5 (between add nodeB and update nodeB)
  {
    type: 'bulk_nodes',
    roomId: 'test-room',
    userId: 'user2',
    data: {
      nodes: [
        { id: 'nodeC', type: 'custom', position: { x: 300, y: 300 }, data: { label: 'Node C from bulk' } }
      ]
    },
    timestamp: 1001.5
  },
  
  ...testChanges.slice(2) // Update nodeB, Update nodeA
]

console.log('With bulk operation at timestamp 1001.5:')
console.log('- Granular changes before bulk (Add nodeA, Add nodeB) are discarded')
console.log('- Bulk operation replaces all nodes with [nodeC]')
console.log('- Granular changes after bulk (Update nodeB, Update nodeA) are accumulated')
console.log('- Final result: nodeC from bulk + failed updates (nodeA/B don\'t exist)')
console.log('- This demonstrates proper timestamp-based precedence')
